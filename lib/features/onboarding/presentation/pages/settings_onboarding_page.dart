import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../generated/l10n/app_localizations.dart';
import '../../providers/onboarding_provider.dart';
import '../widgets/onboarding_step_indicator.dart';
import '../widgets/personal_info_step.dart';
import '../widgets/physical_info_step.dart';
import '../widgets/health_info_step.dart';
import '../widgets/goals_step.dart';
import '../widgets/preferences_step.dart';
import '../widgets/notifications_step.dart';
import '../widgets/settings_save_step.dart';

class SettingsOnboardingPage extends ConsumerStatefulWidget {
  const SettingsOnboardingPage({super.key});

  @override
  ConsumerState<SettingsOnboardingPage> createState() => _SettingsOnboardingPageState();
}

class _SettingsOnboardingPageState extends ConsumerState<SettingsOnboardingPage> {
  late PageController _pageController;

  // Define steps for settings (excluding complete step)
  static const List<OnboardingStep> settingsSteps = [
    OnboardingStep.personalInfo,
    OnboardingStep.physicalInfo,
    OnboardingStep.healthInfo,
    OnboardingStep.goals,
    OnboardingStep.preferences,
    OnboardingStep.notifications,
  ];

  @override
  void initState() {
    super.initState();
    final currentStep = ref.read(onboardingNotifierProvider).currentStep;

    // Find the index of current step in settings steps, default to 0 if not found
    int initialIndex = settingsSteps.indexOf(currentStep);
    if (initialIndex == -1) {
      initialIndex = 0;
      // Set the step to the first settings step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(onboardingNotifierProvider.notifier).goToStep(settingsSteps[0]);
      });
    }

    _pageController = PageController(initialPage: initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    final l10n = AppLocalizations.of(context);

    // Listen for step changes
    ref.listen(onboardingNotifierProvider, (previous, next) {
      if (previous?.currentStep != next.currentStep) {
        final newIndex = settingsSteps.indexOf(next.currentStep);
        if (newIndex != -1 && _pageController.hasClients) {
          _pageController.animateToPage(
            newIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'إدارة الخطة الشخصية',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          if (kDebugMode)
            IconButton(
              icon: const Icon(Icons.bug_report),
              tooltip: 'Pre-fill with test data',
              onPressed: () {
                onboardingNotifier.preFillWithTestData();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Settings data pre-filled with test values'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
            ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: OnboardingStepIndicator(
                currentStep: onboardingState.currentStep,
                progress: _calculateSettingsProgress(onboardingState.currentStep),
                totalSteps: settingsSteps.length,
                steps: settingsSteps,
              ),
            ),

            // Step content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  if (index < settingsSteps.length) {
                    onboardingNotifier.goToStep(settingsSteps[index]);
                  }
                },
                children: [
                  PersonalInfoStep(),
                  PhysicalInfoStep(),
                  HealthInfoStep(),
                  GoalsStep(),
                  PreferencesStep(),
                  NotificationsStep(),
                ],
              ),
            ),

            // Navigation buttons
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // Previous button
                  if (_getCurrentStepIndex() > 0)
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _goToPreviousStep(),
                        child: const Text('السابق'),
                      ),
                    ),

                  if (_getCurrentStepIndex() > 0)
                    const SizedBox(width: 16),

                  // Next/Save button
                  Expanded(
                    flex: _getCurrentStepIndex() == 0 ? 1 : 2,
                    child: ElevatedButton(
                      onPressed: _isCurrentStepValid() ? () => _goToNextStepOrSave() : null,
                      child: Text(_isLastStep() ? 'حفظ' : 'التالي'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  int _getCurrentStepIndex() {
    final currentStep = ref.read(onboardingNotifierProvider).currentStep;
    return settingsSteps.indexOf(currentStep);
  }

  bool _isLastStep() {
    return _getCurrentStepIndex() == settingsSteps.length - 1;
  }

  bool _isCurrentStepValid() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final currentStep = onboardingState.currentStep;
    return onboardingState.stepValidation[currentStep] ?? false;
  }

  void _goToPreviousStep() {
    final currentIndex = _getCurrentStepIndex();
    if (currentIndex > 0) {
      final previousStep = settingsSteps[currentIndex - 1];
      ref.read(onboardingNotifierProvider.notifier).goToStep(previousStep);
    }
  }

  void _goToNextStepOrSave() {
    final currentIndex = _getCurrentStepIndex();
    if (_isLastStep()) {
      // Show save step
      _showSaveStep();
    } else if (currentIndex < settingsSteps.length - 1) {
      final nextStep = settingsSteps[currentIndex + 1];
      ref.read(onboardingNotifierProvider.notifier).goToStep(nextStep);
    }
  }

  void _showSaveStep() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const SettingsSaveStep(),
    );
  }

  double _calculateSettingsProgress(OnboardingStep currentStep) {
    final currentIndex = settingsSteps.indexOf(currentStep);
    if (currentIndex == -1) return 0.0;
    return (currentIndex + 1) / settingsSteps.length;
  }
}
