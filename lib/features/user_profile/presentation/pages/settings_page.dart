import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../generated/l10n/app_localizations.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildSettingsSection(
            context,
            'الإعدادات الشخصية',
            [
              _buildSettingsItem(
                context,
                'المعلومات الشخصية والصحية',
                'تعديل الملف الشخصي، الأهداف الصحية، والتفضيلات الغذائية',
                Icons.person,
                () => context.push('/profile/settings/personal'),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildSettingsSection(
            context,
            'إعدادات التطبيق',
            [
              _buildSettingsItem(
                context,
                'الإشعارات',
                'إدارة إشعارات الوجبات والتذكيرات',
                Icons.notifications,
                () {
                  // TODO: Navigate to notifications settings
                },
              ),
              _buildSettingsItem(
                context,
                'اللغة',
                'تغيير لغة التطبيق',
                Icons.language,
                () {
                  // TODO: Navigate to language settings
                },
              ),
              _buildSettingsItem(
                context,
                'الوحدات',
                'تغيير وحدات القياس (متري/إمبراطوري)',
                Icons.straighten,
                () {
                  // TODO: Navigate to units settings
                },
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildSettingsSection(
            context,
            'الاشتراك والدعم',
            [
              _buildSettingsItem(
                context,
                'الاشتراك المميز',
                'إدارة اشتراكك المميز',
                Icons.star,
                () {
                  // TODO: Navigate to subscription
                },
              ),
              _buildSettingsItem(
                context,
                'المساعدة والدعم',
                'الحصول على المساعدة والتواصل مع الدعم',
                Icons.help,
                () {
                  // TODO: Navigate to help
                },
              ),
              _buildSettingsItem(
                context,
                'حول التطبيق',
                'معلومات حول التطبيق والإصدار',
                Icons.info,
                () {
                  // TODO: Navigate to about
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context,
    String title,
    List<Widget> items,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        Card(
          elevation: 2,
          child: Column(
            children: items,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
      ),
      onTap: onTap,
    );
  }
}
