import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../generated/l10n/app_localizations.dart';
import '../../../onboarding/providers/onboarding_provider.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الخطة'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildSettingsSection(
            context,
            ref,
            'إعدادات الخطة الشخصية',
            [
              _buildStepItem(
                context,
                ref,
                'المعلومات الشخصية',
                'الاسم، تاريخ الميلاد، الجنس، ورقم الهاتف',
                Icons.person,
                OnboardingStep.personalInfo,
              ),
              _buildStepItem(
                context,
                ref,
                'المعلومات الجسدية',
                'الطول، الوزن، مستوى النشاط، والسعرات المحروقة',
                Icons.fitness_center,
                OnboardingStep.physicalInfo,
              ),
              _buildStepItem(
                context,
                ref,
                'المعلومات الصحية',
                'الحساسية، القيود الغذائية، والحالات الصحية',
                Icons.health_and_safety,
                OnboardingStep.healthInfo,
              ),
              _buildStepItem(
                context,
                ref,
                'الأهداف',
                'الهدف الصحي، الوزن المستهدف، والسعرات اليومية',
                Icons.flag,
                OnboardingStep.goals,
              ),
              _buildStepItem(
                context,
                ref,
                'التفضيلات الغذائية',
                'المكونات المفضلة، المكونات المكروهة، والمأكولات المفضلة',
                Icons.restaurant_menu,
                OnboardingStep.preferences,
              ),
              _buildStepItem(
                context,
                ref,
                'الإشعارات',
                'تذكيرات الوجبات، الماء، التمارين، وتحديثات التقدم',
                Icons.notifications,
                OnboardingStep.notifications,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context,
    String title,
    List<Widget> items,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        Card(
          elevation: 2,
          child: Column(
            children: items,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
      ),
      onTap: onTap,
    );
  }
}
